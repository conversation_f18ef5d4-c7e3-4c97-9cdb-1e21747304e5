<?php

declare(strict_types=1);

namespace App\Security\Integrations\Exceptions;

class IntegrationLdapException extends \Exception
{
    public static function noConfigurationFound(): self
    {
        return new self('LDAP_NO_CONFIGURATION');
    }

    public static function ldapException(\Throwable $previous): self
    {
        return new self('LDAP_ERROR', $previous->getCode(), $previous);
    }

    public static function authenticationFailed(): self
    {
        return new self('LDAP_AUTHENTICATION_FAILED');
    }

    public static function userSearchFailed(?\Throwable $previous = null): self
    {
        return new self('LDAP_SEARCH_FAILED', $previous ? $previous->getCode() : 0, $previous);
    }

    public static function noUsersFound(): self
    {
        return new self('LDAP_NO_USERS_FOUND');
    }

    public static function userMappingError(?\Throwable $previous = null): self
    {
        return new self('LDAP_USER_MAPPING_ERROR', $previous ? $previous->getCode() : 0, $previous);
    }

    public static function configurationFieldNotFound(string $name): self
    {
        return new self(json_encode(['LDAP_CONFIG_FIELDS_NOT_FOUND' => $name]));
    }
}
