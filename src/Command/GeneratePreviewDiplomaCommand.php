<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\TypeDiploma;
use App\Service\Diploma\DiplomaService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class GeneratePreviewDiplomaCommand extends Command
{
    protected static $defaultName = 'app:generate-preview-diploma';

    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly DiplomaService $diplomaService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Generates diploma previews')
            ->addArgument('ids', InputArgument::OPTIONAL | InputArgument::IS_ARRAY, 'Diploma type IDs (can specify multiple)')
            ->addOption(
                'all',
                'a',
                InputOption::VALUE_NONE,
                'Generate previews for all diploma types'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $ids = $input->getArgument('ids');
        $generateAll = $input->getOption('all');

        // Validate arguments
        if (!$generateAll && empty($ids)) {
            $io->error('You must specify at least one ID or use the --all option');
            $io->note('Examples:');
            $io->listing([
                'symfony console app:generate-preview-diploma 1',
                'symfony console app:generate-preview-diploma 1 2 3',
                'symfony console app:generate-preview-diploma --all'
            ]);
            return Command::FAILURE;
        }

        if ($generateAll && !empty($ids)) {
            $io->warning('Specified IDs will be ignored because --all option was used');
        }

        // Get diploma types to process
        if ($generateAll) {
            $typeDiplomas = $this->em->getRepository(TypeDiploma::class)->findAll();
            $io->note('Generating previews for all diploma types...');
        } else {
            $typeDiplomas = [];
            foreach ($ids as $id) {
                $typeDiploma = $this->em->getRepository(TypeDiploma::class)->find($id);
                if (!$typeDiploma) {
                    $io->error("Diploma type with ID: $id not found");
                    return Command::FAILURE;
                }
                $typeDiplomas[] = $typeDiploma;
            }
            $io->note(sprintf('Generating previews for %d diploma type(s)...', count($typeDiplomas)));
        }

        return $this->generatePreviews($typeDiplomas, $io);
    }

    /**
     * Generates previews for a list of diploma types
     */
    private function generatePreviews(array $typeDiplomas, SymfonyStyle $io): int
    {
        $successCount = 0;
        $errorCount = 0;

        foreach ($typeDiplomas as $typeDiploma) {
            try {
                // Check if diploma type has preview configuration
                $extra = json_decode($typeDiploma->getExtra() ?? '{}', true);

                if (!isset($extra['templatePreview']) || !isset($extra['previewDiploma'])) {
                    $io->warning(sprintf('Diploma type "%s" (ID: %d) has no preview configuration',
                        $typeDiploma->getName(), $typeDiploma->getId()));
                    continue;
                }

                $io->text(sprintf('Generating preview for: %s (ID: %d)',
                    $typeDiploma->getName(), $typeDiploma->getId()));

                $this->diplomaService->generatePreview($typeDiploma);
                $io->success(sprintf('✓ Preview generated for: %s', $typeDiploma->getName()));
                $successCount++;

            } catch (\Exception $e) {
                $io->error(sprintf('✗ Error generating preview for "%s": %s',
                    $typeDiploma->getName(), $e->getMessage()));
                $errorCount++;
            }
        }

        // Final summary
        if ($successCount > 0) {
            $io->success(sprintf('%d previews generated successfully.', $successCount));
        }

        if ($errorCount > 0) {
            $io->warning(sprintf('There were %d errors generating previews.', $errorCount));
            return Command::FAILURE;
        }

        if ($successCount === 0) {
            $io->warning('No previews were generated.');
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
