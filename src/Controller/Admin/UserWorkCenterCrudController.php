<?php

namespace App\Controller\Admin;

use App\Admin\Field\FundaeCatalogsStateField;
use App\Entity\UserWorkCenter;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserWorkCenterCrudController extends AbstractCrudController
{
    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    public static function getEntityFqcn(): string
    {
        return UserWorkCenter::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->setEntityLabelInPlural($this->translator->trans('fundae_catalogs.user_work_center.label_in_plural', [], 'messages'))
            ->setEntityLabelInSingular($this->translator->trans('fundae_catalogs.user_work_center.label_in_singular', [], 'messages'))
            ->overrideTemplate('crud/index', 'admin/fundaeCatalog/catalogIndex.html.twig')
            ;
    }

    public function configureFields(string $pageName): iterable
    {
        if ($pageName === Crud::PAGE_INDEX) yield IdField::new('id');
        yield TextField::new('name', $this->translator->trans('common_areas.name', [], 'messages'))->setColumns('col-xs-12 col-md-6');
        yield FundaeCatalogsStateField::new('state', $this->translator->trans('fundae_catalogs.fields.state.title', [], 'messages'))
            ->setColumns('col-xs-12 col-md-6');
        if ($pageName !== Crud::PAGE_INDEX)
            yield TextareaField::new('description', $this->translator->trans('course_section.configureFields.description', [], 'messages'))
                ->setColumns('col-12');
    }
}
