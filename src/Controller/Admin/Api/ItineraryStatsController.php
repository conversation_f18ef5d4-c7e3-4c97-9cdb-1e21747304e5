<?php

declare(strict_types=1);

namespace App\Controller\Admin\Api;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\GlobalFilter;
use App\Service\Course\Stats\General\CourseStatsService;
use App\Service\Course\Stats\Persons\CoursePersonService;
use App\Service\Itinerary\Stats\StatsGeneralService;
use App\Service\SettingsService;
use App\Service\StatsUser\ResultGameService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Enum\StatsReportType;
use App\Service\ZipFileTask\ZipFileTaskService;
        

/**
 * @Route("/admin/api/v1/itinerary-stats")
 */
class ItineraryStatsController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private ResultGameService $resultGameService;
    private LoggerInterface $logger;
    private ParameterBagInterface $params;
    private SettingsService $settings;    
    private GlobalFilter $globalFilter;
    private CoursePersonService $coursePersonService;
    private CourseStatsService $generalCourseStatsService;
    private UserCourseService $userCourseService;
    private StatsGeneralService $statsGeneralService;
    private ZipFileTaskService $zipFileTaskService;

    public function __construct(
        EntityManagerInterface $em,
        ResultGameService $resultGameService,
        LoggerInterface $logger,
        ParameterBagInterface $params,
        SettingsService $settings,      
        GlobalFilter $globalFilter,
        CoursePersonService $coursePersonService,
        CourseStatsService $generalCourseStatsService,
        UserCourseService $userCourseService,
        StatsGeneralService $statsGeneralService,
        ZipFileTaskService $zipFileTaskService
    ) {
        $this->em = $em;
        $this->resultGameService = $resultGameService;
        $this->logger = $logger;
        $this->params = $params;
        $this->settings = $settings;       
        $this->globalFilter = $globalFilter;
        $this->coursePersonService = $coursePersonService;
        $this->generalCourseStatsService = $generalCourseStatsService;
        $this->userCourseService = $userCourseService;
        $this->statsGeneralService = $statsGeneralService;
        $this->zipFileTaskService = $zipFileTaskService;
    }

    /**
     * @Rest\Route("/{id}/chapters-stats", methods={"GET","POST"})
     */
    public function getChaptersStats(Itinerary $itinerary): Response
    {
        try {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->statsGeneralService->getStats($itinerary),
                'pathChaptersImages' => $this->params->get('app.chapter_uploads_path') . '/',
                'pathCoursesImages' => $this->params->get('app.course_uploads_path') . '/'
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Error in getChaptersStatsV2', [
                'exception' => $e,
                'courseId' => $itinerary->getId()
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'An unexpected error occurred. Please try again later.',
                'trace' => 'dev' === $_ENV['APP_ENV'] ? $e->getTrace() : [],
            ]);
        }
    }

    /**
     * @Rest\Post("/{id}/xlsx")
     */
    public function generateReport(Request $request, Itinerary $itinerary): Response
    {
        $content = json_decode($request->getContent(), true);
        if (!$content) {
            throw new \Exception("No se recibieron datos en la solicitud.");
        }

        $content['itineraryId'] = $itinerary->getId();
        $fileName = '';
        $userId = $content['userId'] ?? null;
        
        if (!$userId) {
            throw new \Exception("Se requiere el ID de usuario.");
        }
        
        $user = $this->em->getRepository(User::class)->find($userId);
        if (!$user) {
            throw new \Exception("Usuario no encontrado.");
        }
        
        $fileName .= $itinerary->getName() . '_' . $user->getFullName();
        
        $this->zipFileTaskService->enqueueZipFileTask(
            $this->getUser(),
            'course_perCourseStatsReport',
            $content,
            StatsReportType::ITINERARY,
            $fileName . '_' . (new \DateTimeImmutable())->format('d-m-Y'),
            $itinerary->getId()
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $content,
        ]);
    }
}
