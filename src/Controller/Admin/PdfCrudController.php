<?php

namespace App\Controller\Admin;

use App\Entity\Chapter;
use App\Entity\Pdf;
use App\Entity\User;
use App\Service\FilesManager\FilesManagerService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vich\UploaderBundle\Form\Type\VichFileType;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PdfCrudController extends AbstractCrudController
{
    private $em;
    private $logger;
    private $requestStack;
    private TranslatorInterface $translator;
    private FilesManagerService $filesManagerService;

    private const MIME_TYPE_PDF = 'application/pdf';

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger, RequestStack $requestStack, TranslatorInterface $translator, FilesManagerService $filesManagerService)
    {
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->filesManagerService = $filesManagerService;
    }

    public static function getEntityFqcn(): string
    {
        return Pdf::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $pdf_package = Field::new('pdfPackage')->setFormType(VichFileType::class)->setCustomOptions([            
            'constraints' => [
                   new File([
                    'maxSize' => '1024k',
                    'mimeTypes' => [
                        'application/pdf',
                        'application/x-pdf',
                    ],                     
                ])
            ],
           
        ])->setFormTypeOptions([            
            'attr' => [
                'accept' => 'application/pdf'
            ]
        ]);

        $isDownloadable = BooleanField::new('is_downloadable', $this->translator->trans('pdf.downloadable', [], 'messages', $this->getUser()->getLocale()));

        return [
            $pdf_package, $isDownloadable
        ];
    }

    public function configureActions (Actions $actions): Actions
    {
        return $actions           
            ->remove(Crud::PAGE_NEW, Action::SAVE_AND_ADD_ANOTHER);            
    }

    public function createEntity(string $entityFqc)
    {
        $pdf = new pdf();

        $chapterRepository = $this->em->getRepository(Chapter::class);
        $chapter = $chapterRepository->find($this->requestStack->getCurrentRequest()->get('chapterId'));

        $pdf->setChapter($chapter);

        return $pdf;
    }

     /**
     * @Rest\Get("/pdf/{id}", name="pdf_get")     
     */
    public function getPdf(Chapter $chapter, JWTManager $JWT)
    {
        if($this->isGranted('IS_AUTHENTICATED_FULLY')){
            $user = $this->em->getRepository(User::class)->findOneBy(array('email' => $this->getUser()->getUsername()));
            $token = $JWT->create($this->getUser());
        }
        else {
            $user = null;
            $token = null;
        }

        $pdf = $this->em->getRepository(Pdf::class)->findOneBy([
            'chapter' => $chapter
        ]);  
        
        return $this->render('admin/pdf/player_pdf.html.twig', [
            'chapter' => $chapter, 
            'pdf' => $pdf,
            'user' => $user,
            'token' => $token,
            ]);
    }

    /**
     * @Route("/admin/new-pdf/chapter/{chapter}", name="new_pdf", methods={"GET","POST"})
     */
    public function newPdf(Request $request, Chapter $chapter)
    {
        try {
            $file = $request->files->get('file');
            $isDownload = $request->get('isDonwload');
            $referrer = $request->request->get('referrer', $request->headers->get('referer')); // Obtener la referencia con fallback


            if (!$this->filesManagerService->validateFile($file, [self::MIME_TYPE_PDF])) {
                throw new \RuntimeException('Invalid file type. Please upload a valid PDF.');
            }

            $pdf = new Pdf();
            $pdf->setPdf($file->getClientOriginalName());
            $pdf->setPdfPackage($file);
            $pdf->setIsDownloadable($isDownload ? $isDownload : 0);
            $pdf->setChapter($chapter);

            $this->em->persist($pdf);
            $this->em->flush();

            $this->addFlash('success', $this->translator->trans('chapter.message_pdf_success'));
        } catch (\Exception $e) {
            $this->addFlash('danger', $this->translator->trans('chapter.message_pdf_error') . $e->getMessage());
        }

              

        if (!$referrer) {
            $referrer = $this->generateUrl('admin_dashboard'); // Redirección por defecto si no hay referrer
        }
    
        return $this->redirect($referrer);
    }
}
