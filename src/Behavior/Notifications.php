<?php

namespace App\Behavior;

use Symfony\Component\Yaml\Yaml;
use Symfony\Component\Mime\Address;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;

trait Notifications
{
    protected function sendEmailBygroups ($user, $subject, $title, $line1, $line2, $button, $urlButton, $footer)
    {
        print_r($user);
        $pathConf = Yaml::parse(file_get_contents(realpath(__DIR__.'/../..') . '/config/services/easylearning.yaml'));
        $urlBase = Yaml::parse(file_get_contents(realpath(__DIR__.'/../..') . '/config/services/easylearning.yaml'))['parameters']['app.vimeoUploadUrl'];

        foreach ($user['email'] as $emailsToSend) {
            print_r($emailsToSend);
            $email = (new TemplatedEmail())
            ->from(new Address($pathConf['parameters']['app.fromEmail'], $pathConf['parameters']['app.fromName']))
            ->bcc($emailsToSend)
            ->subject($subject)
            ->htmlTemplate('emails/general.html.twig')
            ->context([
                'title'     => $title,
                'line1'     => $line1,
                'line2'     => $line2,
                'button'    => $button,
                'footer'    => $footer,
                'url'   => $urlBase . $urlButton
            ]);
            $this->mailer->send($email);
            sleep(0.5);
        }
    }

    protected function readUsers ($data, $defaultSend = 0)
    {
        $email = '';
        $userIdArr = [];
        $cont = 0;
        $maxByFor = isset($_ENV['EMAIL_MAX_BY_FOR']) ? $_ENV['EMAIL_MAX_BY_FOR'] : 0;
        if ($defaultSend !== 0) {
            $maxByFor = $defaultSend;
        }
        $emailArr = [];
        foreach ($data as $item) {
            $cont++;
            if ($email === '') {
                $email = $item['email'];
            } else {
                $email = $email .',' . $item['email'];
            }
            array_push($userIdArr, $item['id']);
            if ($cont>=$maxByFor && $maxByFor!==0) {
                array_push($emailArr, $email);
                $email = '';
                $cont = 0;
            }
        }

        if (empty($emailArr)) {
            array_push($emailArr, $email);
        }

        return [
            'email'     => $emailArr,
            'id'        => $userIdArr,
        ];
    }
}
