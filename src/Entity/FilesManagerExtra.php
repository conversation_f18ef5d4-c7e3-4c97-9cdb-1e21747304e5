<?php

namespace App\Entity;

use App\Repository\FilesManagerExtraRepository;
use App\Service\Annoucement\Report\AnnouncementReportService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * Store extra information without affecting main FilesManager class
 * @ORM\Entity(repositoryClass=FilesManagerExtraRepository::class)
 */
class FilesManagerExtra
{
    public const TYPE_ANNOUNCEMENT_REPORT = AnnouncementReportService::TYPE_ANNOUNCEMENT;
    public const TYPE_ANNOUNCEMENT_GROUP_REPORT = AnnouncementReportService::TYPE_GROUP;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private ?string $type;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private ?string $entityId;

    /**
     * @ORM\OneToMany(targetEntity=FilesManager::class, mappedBy="filesManagerExtra")
     */
    private $filesManager;

    public function __construct()
    {
        $this->filesManager = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection<int, FilesManager>
     */
    public function getFilesManager(): Collection
    {
        return $this->filesManager;
    }

    public function addFilesManager(FilesManager $filesManager): self
    {
        if (!$this->filesManager->contains($filesManager)) {
            $this->filesManager[] = $filesManager;
            $filesManager->setFilesManagerExtra($this);
        }

        return $this;
    }

    public function removeFilesManager(FilesManager $filesManager): self
    {
        if ($this->filesManager->removeElement($filesManager)) {
            // set the owning side to null (unless already changed)
            if ($filesManager->getFilesManagerExtra() === $this) {
                $filesManager->setFilesManagerExtra(null);
            }
        }

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getEntityId(): ?string
    {
        return $this->entityId;
    }

    public function setEntityId(string $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }
}
