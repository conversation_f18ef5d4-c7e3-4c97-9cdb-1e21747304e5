<?php

namespace App\Entity;

use App\Repository\TypeCourseAnnouncementStepCreationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;

/**
 * @ORM\Entity(repositoryClass=TypeCourseAnnouncementStepCreationRepository::class)
 */
class TypeCourseAnnouncementStepCreation  implements TranslatableInterface
{
    use TranslatableTrait; 
    
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $description;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $position;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isRequired;

    /**
     * @ORM\ManyToOne(targetEntity=TypeCourse::class, inversedBy="typeCourseAnnouncementStepCreations")
     * @ORM\JoinColumn(nullable=false)
     */
    private $typeCourse;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementStepCreation::class, inversedBy="typeCourseAnnouncementStepCreations")
     */
    private $announcementStepCreation;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $extra = [];

    /**
     * @ORM\OneToMany(targetEntity=TypeCourseAnnouncementStepConfiguration::class, mappedBy="typeCourseAnnouncementStepCreation")
     */
    private $typeCourseAnnouncementStepConfigurations;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $active;

    public function __construct()
    {
        $this->typeCourseAnnouncementStepConfigurations = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(?int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function isIsRequired(): ?bool
    {
        return $this->isRequired;
    }

    public function setIsRequired(?bool $isRequired): self
    {
        $this->isRequired = $isRequired;

        return $this;
    }

    public function getTypeCourse(): ?TypeCourse
    {
        return $this->typeCourse;
    }

    public function setTypeCourse(?TypeCourse $typeCourse): self
    {
        $this->typeCourse = $typeCourse;

        return $this;
    }

    public function getAnnouncementStepCreation(): ?AnnouncementStepCreation
    {
        return $this->announcementStepCreation;
    }

    public function setAnnouncementStepCreation(?AnnouncementStepCreation $announcementStepCreation): self
    {
        $this->announcementStepCreation = $announcementStepCreation;

        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    /**
     * @return Collection<int, TypeCourseAnnouncementStepConfiguration>
     */
    public function getTypeCourseAnnouncementStepConfigurations(): Collection
    {
        return $this->typeCourseAnnouncementStepConfigurations;
    }

    public function addTypeCourseAnnouncementStepConfiguration(TypeCourseAnnouncementStepConfiguration $typeCourseAnnouncementStepConfiguration): self
    {
        if (!$this->typeCourseAnnouncementStepConfigurations->contains($typeCourseAnnouncementStepConfiguration)) {
            $this->typeCourseAnnouncementStepConfigurations[] = $typeCourseAnnouncementStepConfiguration;
            $typeCourseAnnouncementStepConfiguration->setTypeCourseAnnouncementStepCreation($this);
        }

        return $this;
    }

    public function removeTypeCourseAnnouncementStepConfiguration(TypeCourseAnnouncementStepConfiguration $typeCourseAnnouncementStepConfiguration): self
    {
        if ($this->typeCourseAnnouncementStepConfigurations->removeElement($typeCourseAnnouncementStepConfiguration)) {
            // set the owning side to null (unless already changed)
            if ($typeCourseAnnouncementStepConfiguration->getTypeCourseAnnouncementStepCreation() === $this) {
                $typeCourseAnnouncementStepConfiguration->setTypeCourseAnnouncementStepCreation(null);
            }
        }

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): self
    {
        $this->active = $active;

        return $this;
    }
}
