<?php

namespace App\Entity;

use App\Repository\HelpTextRepository;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=HelpTextRepository::class)
 */
class HelpText implements TranslatableInterface
{
    use TranslatableTrait;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     * @Groups({"help"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"help"})
     */
    private $title;

    /**
     * @ORM\Column(type="text")
     * @Groups({"help"})
     */
    private $text;

    /**
     * @ORM\ManyToOne(targetEntity=HelpCategory::class, inversedBy="helpTexts")
     */
    private $category;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(string $text): self
    {
        $this->text = $text;

        return $this;
    }

    public function getCategory(): ?HelpCategory
    {
        return $this->category;
    }

    public function setCategory(?HelpCategory $category): self
    {
        $this->category = $category;

        return $this;
    }
}
