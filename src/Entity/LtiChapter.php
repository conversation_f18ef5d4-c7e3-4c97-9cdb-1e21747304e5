<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\LtiChapterRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=LtiChapterRepository::class)
 */
class LtiChapter
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private ?int $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private ?string $identifier;

    /**
     * @ORM\OneToOne(targetEntity=Chapter::class, inversedBy="lti", cascade={"persist", "remove"})
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?Chapter $chapter;

    /**
     * @ORM\ManyToOne(targetEntity=LtiTool::class, inversedBy="ltiChapter")
     */
    private $ltiTool;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getIdentifier(): ?string
    {
        return $this->identifier;
    }

    public function setIdentifier(string $identifier): self
    {
        $this->identifier = $identifier;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getLtiTool()
    {
        return $this->ltiTool;
    }

    public function setLtiTool($ltiTool): self
    {
        $this->ltiTool = $ltiTool;

        return $this;
    }
}
