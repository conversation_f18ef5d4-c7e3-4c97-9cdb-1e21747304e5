<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\ItineraryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=ItineraryRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class Itinerary implements TranslatableInterface
{
    use TranslatableTrait;
    use AtAndBy;
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list", "detail"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list", "detail"})
     */
    private $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $description;

    /**
     * @ORM\OneToMany(targetEntity=ItineraryCourse::class, mappedBy="itinerary", orphanRemoval=true, cascade={"remove","persist"})
     *
     * @ORM\OrderBy({"position" = "ASC"})
     */
    private $itineraryCourses;

    /**
     * @ORM\OneToMany(targetEntity=ItineraryUser::class, mappedBy="itinerary", orphanRemoval=true, cascade={"remove","persist"})
     */
    private $itineraryUsers;

    /**
     * @ORM\OneToMany(targetEntity=ItineraryManager::class, mappedBy="itinerary", orphanRemoval=true, cascade={"remove","persist"})
     */
    private $itineraryManagers;

    /**
     * @ORM\ManyToMany(targetEntity=Filter::class, inversedBy="itineraries")
     */
    private $filters;

    /**
     * @ORM\OneToMany(targetEntity=EmailNotification::class, mappedBy="itinerary")
     */
    private $emailNotificationUser;

    /**
     * @ORM\Column(type="datetime", nullable=false)
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $updatedAt;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $tags;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $active;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $sort;

    public function __construct()
    {
        $this->itineraryCourses = new ArrayCollection();
        $this->itineraryUsers = new ArrayCollection();
        $this->itineraryManagers = new ArrayCollection();
        $this->filters = new ArrayCollection();
        $this->tags = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection<int, ItineraryCourse>
     */
    public function getItineraryCourses(): Collection
    {
        return $this->itineraryCourses;
    }

    public function addItineraryCourse(ItineraryCourse $itineraryCourse): self
    {
        if (!$this->itineraryCourses->contains($itineraryCourse)) {
            $this->itineraryCourses[] = $itineraryCourse;
            $itineraryCourse->setItinerary($this);
        }

        return $this;
    }

    public function removeItineraryCourse(ItineraryCourse $itineraryCourse): self
    {
        if ($this->itineraryCourses->removeElement($itineraryCourse)) {
            // set the owning side to null (unless already changed)
            if ($itineraryCourse->getItinerary() === $this) {
                $itineraryCourse->setItinerary(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->name;
    }

    /**
     * @return Collection<int, ItineraryUser>
     */
    public function getItineraryUsers(): Collection
    {
        return $this->itineraryUsers;
    }

    public function addItineraryUser(ItineraryUser $itineraryUser): self
    {
        if (!$this->itineraryUsers->contains($itineraryUser)) {
            $this->itineraryUsers[] = $itineraryUser;
            $itineraryUser->setItinerary($this);
        }

        return $this;
    }

    public function removeItineraryUser(ItineraryUser $itineraryUser): self
    {
        if ($this->itineraryUsers->removeElement($itineraryUser)) {
            // set the owning side to null (unless already changed)
            if ($itineraryUser->getItinerary() === $this) {
                $itineraryUser->setItinerary(null);
            }
        }

        return $this;
    }

    public function removeAllItineraryUser(): self
    {
        foreach ($this->itineraryUsers as $itineraryUser) {
            $this->removeItineraryUser($itineraryUser);
        }

        return $this;
    }

    /**
     * @return Collection<int, ItineraryManager>
     */
    public function getItineraryManagers(): Collection
    {
        return $this->itineraryManagers;
    }

    public function addItineraryManager(ItineraryManager $itineraryManager): self
    {
        if (!$this->itineraryManagers->contains($itineraryManager)) {
            $this->itineraryManagers[] = $itineraryManager;
            $itineraryManager->setItinerary($this);
        }

        return $this;
    }

    public function removeItineraryManager(ItineraryManager $itineraryManager): self
    {
        if ($this->itineraryManagers->removeElement($itineraryManager)) {
            // set the owning side to null (unless already changed)
            if ($itineraryManager->getItinerary() === $this) {
                $itineraryManager->setItinerary(null);
            }
        }

        return $this;
    }

    public function removeAllItineraryManagers(): self
    {
        foreach ($this->itineraryManagers as $manager) {
            $this->removeItineraryManager($manager);
        }

        return $this;
    }

    /**
     * @return Collection<int, Filter>
     */
    public function getFilters(): Collection
    {
        return $this->filters;
    }

    public function setFilters($filters): self
    {
        $old = $this->getFilters();
        foreach ($old as $o) {
            if (!\in_array($o, $filters)) {
                $this->removeFilter($o);
            }
        }
        foreach ($filters as $f) {
            $this->addFilter($f);
        }

        return $this;
    }

    public function addFilter(Filter $filter): self
    {
        if (!$this->filters->contains($filter)) {
            $this->filters[] = $filter;
        }

        return $this;
    }

    public function removeFilter(Filter $filter): self
    {
        $this->filters->removeElement($filter);

        return $this;
    }

    public function getTotalCourses(): int
    {
        return \count($this->itineraryCourses);
    }

    public function getFiltersGroupedByCategory(): array
    {
        $filters = [];
        foreach ($this->filters as $filter) {
            $filter->getFilterCategory();
            if (!isset($filters[$filter->getFilterCategory()->getId()])) {
                $filters[$filter->getFilterCategory()->getId()] = [];
            }
            $filters[$filter->getFilterCategory()->getId()][] = $filter;
        }

        return $filters;
    }

    /**
     * Get the value of emailNotificationUser.
     */
    public function getEmailNotificationUser()
    {
        return $this->emailNotificationUser;
    }

    /**
     * Set the value of emailNotificationUser.
     *
     * @return self
     */
    public function setEmailNotificationUser($emailNotificationUser)
    {
        $this->emailNotificationUser = $emailNotificationUser;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getTags(): ?string
    {
        return $this->tags;
    }

    public function setTags(?string $tags): self
    {
        $this->tags = $tags;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort ?? $this->id;
    }

    public function setSort(?int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }
}
