<?php

namespace App\Entity;

use App\Repository\SettingRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=SettingRepository::class)
 */
class Setting
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"setting:list"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"setting:list"})
     */
    private $code;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Groups({"setting:list"})
     */
    private $value;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"setting:list"})
     */
    private $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Groups({"setting:list"})
     */
    private $description;

    /**
     * @ORM\ManyToOne(targetEntity=SettingGroup::class, inversedBy="settings")
     * @ORM\JoinColumn(nullable=false)
     */
    private $settingGroup;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"setting:list"})
     */
    private $sort;

    /**
     * @ORM\Column(type="json")
     * @Groups({"setting:list"})
     */
    private $options = [];

    /**
     * @ORM\Column(type="string", length=60)
     * @Groups({"setting:list"})
     */
    private $type;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue($value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getSettingGroup(): ?SettingGroup
    {
        return $this->settingGroup;
    }

    public function setSettingGroup(?SettingGroup $settingGroup): self
    {
        $this->settingGroup = $settingGroup;

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }

    public function getOptions(): ?array
    {
        return $this->options;
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }


    public function getTransformedValue ()
    {
        switch ($this->getType())
        {
            case 'bool':
                return filter_var($this->getValue(), FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int)$this->getValue();
            case 'float':
                return (float)$this->getValue();
            case 'array':
                return json_decode($this->getValue(), true);
            default:
                return $this->getValue();
        }
    }


    public function setTransformedValue ($value)
    {
        switch ($this->getType())
        {
            case 'boolean':
                $this->setValue((bool)$value);
                break;
            case 'integer':
                $this->setValue((int)$value);
                break;
            case 'float':
                $this->setValue((float)$value);
                break;
            case 'array':
                $this->setValue(json_encode($value));
                break;
            default:
                $this->setValue($value);
        }
    }
}
