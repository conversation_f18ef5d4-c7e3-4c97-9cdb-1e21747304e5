<?php

namespace App\Resources\DataFixtureBase\Announcement;

class ClassroomvirtualTypeData
{

    const DEFAULT_DATA = [
        [
            'id' => 1,
            'name' => 'class_room_virtual.zoom.name',
            'state' => 1,
            'extra' => [
                ['logo' => 'assets_announcement/virtual_class_type/zoom-logo.png'],
            ]
        ],
        [
            'id' => 2,
            'name' => 'class_room_virtual.clickmeeting.name',
            'state' => 1,
            'extra' => [
                ['logo' => 'assets_announcement/virtual_class_type/clickmeeting-logo.png'],
            ]
        ],
        [
            'id' => 3,
            'name' => 'class_room_virtual.jitsi.name',
            'state' => 0,
            'extra' => [
                ['logo' => 'assets_announcement/virtual_class_type/jitsi-logo.png']
            ]
        ],
        [
            'id' => 4,
            'name' => 'class_room_virtual.plugnmeet.name',
            'state' => 1,
            'extra' => [
                ['logo' => 'assets_announcement/virtual_class_type/plug-meet.png']
            ]
        ]
    ];

}