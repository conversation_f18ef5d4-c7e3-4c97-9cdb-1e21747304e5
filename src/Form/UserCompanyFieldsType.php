<?php

namespace App\Form;

use App\Entity\User;
use App\Entity\UserCompany;
use App\Entity\UserFieldsFundae;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserCompanyFieldsType extends AbstractType
{
    private TranslatorInterface $translator;
    private EntityManagerInterface $em;
    public function __construct(TranslatorInterface $translator, EntityManagerInterface $em)
    {
        $this->translator = $translator;
        $this->em = $em;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $companies = $this->em->getRepository(UserCompany::class)->findBy(['state' => true], ['name' => 'ASC']);
        $builder
            ->add('userCompany', EntityType::class, [
                'class' => UserCompany::class,
                'label' => $this->translator->trans('fundae_catalogs.user_company.label_in_singular', [], 'messages'),
                'choice_label' => static function ($choice, string $key, $value) {
                    return $choice ? $choice->getName() : null;
                },
                'choices' => $companies,
                'placeholder' => $this->translator->trans('common_areas.select_choice', [], 'messages'),
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UserFieldsFundae::class,
            'companies'=> null,
        ]);
    }
}
