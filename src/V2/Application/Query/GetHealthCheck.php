<?php

namespace App\V2\Application\Query;

use App\Entity\User;
use App\V2\Domain\Bus\Query;
use App\V2\Domain\User\UserCriteria;

readonly class GetHealthCheck implements Query
{
    public function __construct(
        private UserCriteria $criteria,
        private ?User $requestUser = null
    ){
    }

    public function getCriteria(): UserCriteria
    {
        return $this->criteria;
    }

    public function getRequestUser(): ?User
    {
        return $this->requestUser;
    }
}