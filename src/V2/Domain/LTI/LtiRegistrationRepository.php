<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface LtiRegistrationRepository
{
    /**
     * @throws InfrastructureException
     */
    public function put(LtiRegistration $registration): void;

    /**
     * @throws LtiRegistrationNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(LtiRegistrationCriteria $criteria): LtiRegistration;

    /**
     * @throws InfrastructureException
     */
    public function findBy(LtiRegistrationCriteria $criteria): LtiRegistrationCollection;

    /**
     * @throws InfrastructureException
     */
    public function delete(LtiRegistration $ltiRegistration): void;
}
