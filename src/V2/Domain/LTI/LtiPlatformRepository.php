<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface LtiPlatformRepository
{
    /**
     * @throws InfrastructureException
     */
    public function put(LtiPlatform $platform): void;

    /**
     * @throws LtiPlatformNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(LtiPlatformCriteria $criteria): LtiPlatform;

    /**
     * @throws InfrastructureException
     */
    public function findBy(LtiPlatformCriteria $criteria): LtiPlatformCollection;

    /**
     * @throws InfrastructureException
     */
    public function delete(LtiPlatform $platform): void;
}
