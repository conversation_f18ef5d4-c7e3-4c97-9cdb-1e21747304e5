<?php

declare(strict_types=1);

namespace App\Modules\Survey\Repository;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\NpsQuestion;
use App\Entity\NpsQuestionDetail;
use App\Entity\NpsQuestionTranslation;
use App\Entity\Survey;
use App\Entity\SurveyAnnouncement;
use App\Entity\SurveyCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SurveyCourse>
 *
 * @method SurveyCourse|null find($id, $lockMode = null, $lockVersion = null)
 * @method SurveyCourse|null findOneBy(array $criteria, array $orderBy = null)
 * @method SurveyCourse[]    findAll()
 * @method SurveyCourse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SurveyCourseModuleRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SurveyCourse::class);
    }

    public function getQuestionsSurveyByDefault($locale = null): array
    {
        $surveyMain = $this->getEntityManager()->getRepository(Survey::class)->findOneBy(['isMain' => 1]);

        $questions = $surveyMain ? $this->getNpsQuestionSurveyMain($surveyMain) : null;

        return $this->getStructureQuestion($questions ?? $this->getQuestionNpsMain(), $locale);
    }

    public function getQuestionNpsMain()
    {
        return $this->getNpsQuestionRepository()->findBy(['main' => 1, 'source' => 1]);
    }

    private function getNpsQuestionRepository()
    {
        return $this->getEntityManager()->getRepository(NpsQuestion::class);
    }

    public function getQuestionsSurvey($entity, ?string $locale = null): array
    {
        $survey = ($entity instanceof Course)
            ? $this->getActiveSurveyCourse($entity)
            : $this->getActiveSurveyAnnouncement($entity);

        $questions = $this->getNpsQuestion($entity, $survey);

        if (empty($questions)) {
            return $this->getQuestionsSurveyByDefault($locale);
        }

        return $this->getStructureQuestion($questions, $locale);
    }

    public function getStructureQuestion($questions, $locale = null): array
    {
        $npsQuestion = [];

        usort($questions, function ($a, $b) {
            return $a->getPosition() <=> $b->getPosition();
        });

        foreach ($questions as $question) {
            $npsQuestion[] = [
                'id' => $question->getId(),
                'question' => $this->getQuestionTranslation($question, $locale) ?? $question->getQuestion(),
                'type' => $question->getType(),
                'main' => $question->getMain(),
                'required' => $question->isIsRequired(),
                'confidential' => $question->isIsConfidential(),
                'answers' => $this->getAnswerQuestion($question, $locale),
            ];
        }

        return $npsQuestion;
    }

    private function getAnswerQuestion(NpsQuestion $question, $locale = null): array
    {
        return $question->getNpsQuestionDetails()
            ? $question->getNpsQuestionDetails()
            ->map(function ($detail) use ($locale) {
                return [
                    'id' => $detail->getId(),
                    'value' => $this->getNpsQuestionDetailTranslation($detail, $locale),
                    'position' => $detail->getPosition(),
                ];
            })
            ->toArray()
            : [];
    }

    public function getQuestionTranslation(NpsQuestion $question, $locale = null): ?string
    {
        $nameQuestion = null;
        if ($locale) {
            /** @var NpsQuestionTranslation $translation */
            $translation = $question->translate($locale);
            $nameQuestion = $translation->getQuestion();
        }

        return $nameQuestion;
    }

    public function getNpsQuestionDetailTranslation(NpsQuestionDetail $detail, $locale = null): ?string
    {
        $nameDetail = $detail->getValue();
        if ($locale) {
            /** @var NpsQuestionDetailTranslation $translation */
            $translation = $detail->translate($locale);
            $nameDetail = $translation->getValue() ?? $nameDetail;
        }

        return $nameDetail;
    }

    private function getNpsQuestion($conditionValue, $survey): array
    {
        $em = $this->_em;
        $query = $em->createQueryBuilder();
        $query->from(NpsQuestion::class, 'nq')
            ->select('nq')
            ->andWhere($query->expr()->in('nq.survey', $survey))
            ->andWhere('nq.active = 1')
            ->setParameter('conditionalValue', $conditionValue);

        return $query->getQuery()->getResult();
    }

    private function getNpsQuestionSurveyMain(Survey $survey): array
    {
        $em = $this->_em;
        $query = $em->createQueryBuilder();
        $query->from(NpsQuestion::class, 'nq')
            ->select('nq')
            ->andWhere('nq.survey = :survey')
            ->andWhere('nq.active = 1')
            ->setParameter('survey', $survey->getId());

        return $query->getQuery()->getResult();
    }

    private function getActiveSurveyCourse(Course $course): ?string
    {
        return $this->getActiveSurveyForEntity($course, SurveyCourse::class, 'course');
    }

    private function getActiveSurveyAnnouncement(Announcement $announcement): ?string
    {
        return $this->getActiveSurveyForEntity($announcement, SurveyAnnouncement::class, 'announcement');
    }

    private function getActiveSurveyForEntity($entity, string $entityClass, string $field): ?string
    {
        return $this->_em->createQueryBuilder()
            ->select('s.id')
            ->from($entityClass, 'e')
            ->leftJoin('e.survey', 's')
            ->where("e.$field = :conditionalValue")
            ->andWhere('s.active = 1')
            ->setParameter('conditionalValue', $entity)
            ->setMaxResults(1)
            ->getDQL();
    }
}
