<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\GenericToken;
use App\Entity\User;
use App\Repository\AnnouncementGroupRepository;
use App\Service\SettingsService;
use App\Service\Traits\Announcement\AnnouncementGroupTrait;
use App\Service\Traits\Announcement\AssistanceBySessionTrait;
use App\Utils\DateCalculationUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementGroupAssistanceService
{
    use AnnouncementGroupTrait;
    use AssistanceBySessionTrait;

    private EntityManagerInterface $em;
    private RequestStack $requestStack;
    private TranslatorInterface $translator;
    private SettingsService $settings;
    private Security $security;
    private AnnouncementGroupRepository $announcementGroupRepository;

    public function __construct(
        EntityManagerInterface $em,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        SettingsService $settings,
        Security $security
    ) {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->settings = $settings;
        $this->security = $security;
        $this->announcementGroupRepository = $this->em->getRepository(AnnouncementGroup::class);
    }

    public function getSessionsAnnouncementGroup(Announcement $announcement)
    {
        $usersByGroup = $this->getUsersByGroup($announcement, false);

        $groups = [];
        $numGroup = 1;
        foreach ($usersByGroup as $group => $users) {
            $groupName = $this->translator->trans('announcements.common.group', [], 'messages') . ' ' . $numGroup++;
            $groupInfo = $this->getInformationGroup($announcement, $group);
            $groupCombinedName = $groupInfo['code'] ? $groupName . ' (' . $groupInfo['code'] . ')' : $groupName;
            $groups[] = [
                'name' => $groupCombinedName,
                'total' => \count($users),
                'groupInfo' => $groupInfo,
                'sessions' => $this->getSessionsByGroup($announcement, $group),
            ];
        }

        return $groups;
    }

    public function getSessionsByGroup(Announcement $announcement, $idGroup)
    {
        $announcementGroup = $this->getAnnouncementGroupById($announcement, $idGroup);

        if (!$announcementGroup) {
            return [];
        }

        $sessions = $announcementGroup->getAnnouncementGroupSessions();

        $sessionsByGroup = [];
        $timezone = $announcement->getTimezone();

        foreach ($sessions as $session) {
            if ($session->getStartAt() >= $session->getFinishAt()) {
                continue;
            }
            $sessionInfo = $this->getSessionInfo($session);
            if (empty($sessionInfo['timezone'])) {
                $sessionInfo['timezone'] = $timezone;
            }
            if (!empty($sessionInfo['timezone'])) {
                $dateTimeZone = new \DateTimeZone($sessionInfo['timezone']);
                $date = new \DateTime('now', $dateTimeZone);
                $sessionInfo['timezoneOffset'] = $date->format('P');
            }
            $sessionInfo['assistance'] = $this->getFullUserAssistance($session);

            $sessionsByGroup[] = $sessionInfo;
        }

        return $sessionsByGroup;
    }

    public function getSessions(Announcement $announcement, $idGroup)
    {
        $announcementGroup = $this->getAnnouncementGroupById($announcement, $idGroup);

        if (!$announcementGroup) {
            return [];
        }

        $sessions = $announcementGroup->getAnnouncementGroupSessions();

        $sessionsByGroup = [];

        foreach ($sessions as $session) {
            if ($session->getStartAt() >= $session->getFinishAt()) {
                continue;
            }
            $sessionsByGroup[] = $this->getSessionInfo($session);
        }

        return $sessionsByGroup;
    }

    private function getAnnouncementGroupById(Announcement $announcement, $idGroup)
    {
        return $this->em->getRepository(AnnouncementGroup::class)->findOneBy(['announcement' => $announcement, 'id' => $idGroup]);
    }

    private function getSessionInfo(AnnouncementGroupSession $session)
    {
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();
        $entryToken = $this->em->getRepository(GenericToken::class)->createAnnouncementSessionToken($session, GenericToken::TYPE_ANNOUNCEMENT_SESSION_ENTRY_QR);
        $exitToken = $this->em->getRepository(GenericToken::class)->createAnnouncementSessionToken($session, GenericToken::TYPE_ANNOUNCEMENT_SESSION_EXIT_QR);

        $entryQrTokenData = $entryToken ? $host . '/campus?generic-token=' . $entryToken->getToken() : null;
        $exitQrTokenData = $exitToken ? $host . '/campus?generic-token=' . $exitToken->getToken() : null;

        // Restar 10 minutos por cuestion de preparar la reunion
        $startedAt = $session->getStartAt();
        $startedAt->modify('-10 minutes');

        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();
        $fileAssistance = null;
        $assistanceFiles = [];
        foreach ($session->getAssistanceFiles() as $assistanceFile) {
            $assistanceFiles[] = [
                'id' => $assistanceFile->getId(),
                'filename' => $assistanceFile->getFilename(),
                'originalName' => $assistanceFile->getOriginalName(),
                'mimeType' => $assistanceFile->getMimeType(),
                'fileSize' => $assistanceFile->getFileSize(),
                'url' => $host . '/' . $this->settings->get('app.announcement_assistance_group_session') . $assistanceFile->getFilename(),
            ];
        }

        // Calculate margins
        $diffResult = $session->getFinishAt()->diff($session->getStartAt());
        $sessionTimeInMinutes = $diffResult->days * 24 * 60;
        $sessionTimeInMinutes += $diffResult->h * 60;
        $sessionTimeInMinutes += $diffResult->i;

        $entryMarginInMinutes = floor($sessionTimeInMinutes * $session->getEntryMargin() / 100);
        $exitMarginInMinutes = floor($sessionTimeInMinutes * $session->getExitMargin() / 100);
        $maxEntryDateTime = $session->getStartAt()->modify("+{$entryMarginInMinutes} minutes");
        $minExitDateTime = $session->getFinishAt()->modify("-{$exitMarginInMinutes} minutes");

        return [
            'id' => $session->getId(),
            'startAt' => $startedAt,
            'finishAt' => $session->getFinishAt(),
            'url' => $session->getUrl(),
            'state' => DateCalculationUtils::getStateOfDateRange($startedAt, $session->getFinishAt()),
            'hourInit' => $session->getStartAt()->format('H:i'),
            'hourEnd' => $session->getFinishAt()->format('H:i'),
            'entryQrToken' => $entryQrTokenData,
            'exitQrToken' => $exitQrTokenData,
            'fileAssistance' => $fileAssistance,
            'assistanceFiles' => $assistanceFiles,
            'entryMargin' => $session->getEntryMargin() ?? 0,
            'exitMargin' => $session->getExitMargin() ?? 0,
            'entryMarginMaxTime' => $maxEntryDateTime->format('H:i'),
            'exitMarginMinTime' => $minExitDateTime->format('H:i'),
            'sessionTimeInMinutes' => $sessionTimeInMinutes,
            'timezone' => $session->getTimezone(),
            'timezoneOffset' => '',
            'place' => $session->getPlace(),
        ];
    }

    public function getAsistanceUserBySession(AnnouncementGroupSession $announcementGroupSession, User $user)
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcementGroup' => $announcementGroupSession->getAnnouncementGroup(), 'user' => $user]);
        $assistance = $announcementGroupSession->getAssistance();

        if (!$announcementUser) {
            return false;
        }
        if (null === $assistance) {
            return false;
        }

        $isAssistance = false;

        foreach ($assistance as $assistanceData) {
            if ($assistanceData['id'] === $announcementUser->getUser()->getId()) {
                $isAssistance = $assistanceData['assistance'];
                break;
            }
        }

        return $isAssistance;
    }
}
