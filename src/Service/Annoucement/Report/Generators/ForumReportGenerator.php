<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report\Generators;

use App\Entity\AnnouncementGroup;
use App\Entity\ChatChannel;
use App\Entity\ChatMessage;
use App\Entity\ChatServer;
use App\Entity\User;
use App\Service\Annoucement\Report\AnnouncementContainer;
use App\Service\Annoucement\Report\AnnouncementReportConstants;
use App\Utils\SpreadsheetUtil;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class ForumReportGenerator
{
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;
    private AnnouncementReportConstants $announcementReportConstants;

    public function __construct(
        EntityManagerInterface $entityManager,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->logger = $logger;
        $this->announcementReportConstants = $announcementReportConstants;
    }

    public function generate(
        AnnouncementContainer $announcementContainer,
        string $announcementDir,
        callable $initSheetCallback,
        bool $isOnlineMode
    ): void {
        $headersInfo = $isOnlineMode ? $this->announcementReportConstants::ONLINE_FORUM_HEADERS['info'] : [];

        $report = new SpreadsheetUtil('Reporte del foro', 'Info');

        if (!empty($headersInfo)) {
            $infoData = [];
            foreach ($announcementContainer->announcementGroups as $group) {
                $announcementTutor = $group->getAnnouncementTutor();
                $userTutor = $announcementTutor ? $announcementTutor->getTutor() : null;

                $server = $this->entityManager->getRepository(ChatServer::class)
                    ->findOneBy([
                        'type' => ChatServer::TYPE_ANNOUNCEMENT,
                        'entityId' => $announcementContainer->announcement->getId(),
                    ]);

                if (!$server) {
                    continue;
                }

                $forumChannel = $this->entityManager->getRepository(ChatChannel::class)->getChannelByEntityType(
                    $server,
                    ChatChannel::TYPE_FORUM,
                    $group->getId(),
                    AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP,
                    true
                );

                if (!$forumChannel) {
                    continue;
                }

                $ids = [];
                $threads = $forumChannel->getChatChannels();
                foreach ($threads as $thread) {
                    $this->fillMessagesPage(
                        $report,
                        $thread,
                        'Hilo ' . $thread->getId(),
                        $userTutor,
                        $initSheetCallback
                    );
                    $ids[] = $thread->getId();
                }

                $nUsers = $this->countUsers($ids);
                $nComments = $this->countComments($ids);
                $nTutorComments = $this->countTutorComments($ids, $group, $userTutor);

                $infoData[] = [
                    'id' => $forumChannel->getId(),
                    'code' => $group->getCode() . '_' . $forumChannel->getId(),
                    'threads' => \count($threads),
                    'n_users' => $nUsers,
                    'n_comments' => $nComments,
                    'n_tutor_comments' => $nTutorComments,
                ];
            }

            $initSheetCallback($report, 'Info', $headersInfo);
            $report->fromArray($infoData, '--', 'A2')->alignAllLeft();
        }

        $report->saveReport($announcementDir);
    }

    public function fillMessagesPage(
        SpreadsheetUtil $report,
        ChatChannel $channel,
        string $sheetTitle,
        ?User $userTutor,
        callable $initSheetCallback
    ): void {
        $headersMessagesPage = $this->announcementReportConstants::ONLINE_FORUM_HEADERS['messages_page'];

        $initSheetCallback(
            $report,
            $sheetTitle,
            $headersMessagesPage
        );

        $messagesSQL = $this->entityManager->getRepository(ChatMessage::class)->createQueryBuilder('cm')
            ->select('cm.id as cm_id, u.id as u_id, u.meta as u_code, u.firstName, u.lastName, u.email, cm.createdAt, cm.message')
            ->leftJoin('cm.user', 'u')
            ->where('cm.channel = :channel')
            ->setParameter('channel', $channel)
            ->groupBy('cm.id')
            ->orderBy('cm.createdAt', 'ASC');

        $messages = $messagesSQL
            ->getQuery()
            ->getArrayResult();

        foreach ($messages as &$message) {
            if (\is_array($message['u_code'])) {
                $message['u_code'] = $message['u_code']['HRP'] ?? '';
            } elseif (\is_string($message['u_code'])) {
                $decoded = json_decode($message['u_code'], true);
                $message['u_code'] = $decoded['HRP'] ?? '';
            } else {
                $message['u_code'] = '';
            }
        }

        $sheet = $report->getSheet($sheetTitle);

        $rowIndex = 2;
        foreach ($messages as $messageIterator) {
            $sheet->fromArray([$messageIterator], null, "A{$rowIndex}");

            $sheet->getStyle("A{$rowIndex}:I{$rowIndex}")
                ->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
            
            if ($userTutor && $messageIterator['u_id'] === $userTutor->getId()) {
                $sheet->getStyle("A{$rowIndex}:I{$rowIndex}")
                    ->getFont()
                    ->setBold(true);
            }

            ++$rowIndex;
        }
    }

    private function countUsers(array $ids)
    {
        if (empty($ids)) {
            return 0;
        }

        $qb = $this->entityManager->getRepository(ChatMessage::class)->createQueryBuilder('cm');

        return (int) $qb->select('COUNT(DISTINCT u.id) as total')
            ->leftJoin('cm.user', 'u')
            ->where($qb->expr()->in('cm.channel', ':ids'))
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function countComments(array $ids)
    {
        if (empty($ids)) {
            return 0;
        }

        $qb = $this->entityManager->getRepository(ChatMessage::class)->createQueryBuilder('cm');

        return (int) $qb->select('COUNT(cm.id) as total')
            ->where($qb->expr()->in('cm.channel', ':ids'))
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function countTutorComments(array $ids, AnnouncementGroup $group, ?User $userTutor = null)
    {
        if (empty($ids) || !$userTutor) {
            return 0;
        }

        $qb = $this->entityManager->getRepository(ChatMessage::class)->createQueryBuilder('cm');

        return (int) $qb->select('COUNT(cm.id) as total')
            ->where($qb->expr()->in('cm.channel', ':ids'))
            ->andWhere('cm.user = :tutorId')
            ->setParameter('ids', $ids)
            ->setParameter('tutorId', $userTutor->getId())
            ->getQuery()
            ->getSingleScalarResult();
    }
}
