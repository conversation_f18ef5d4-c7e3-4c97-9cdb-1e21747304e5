<?php

namespace App\Service\Annoucement\ReportPdf;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\ChatChannel;
use App\Entity\Pdf;
use App\Entity\User;
use App\Service\Diploma\DiplomaService;
use App\Service\Annoucement\ReportPdf\Pdf\PdfAnnouncementGroupService;
use App\Service\Annoucement\ReportPdf\User\ReportActivitiesService;
use App\Service\Annoucement\ReportPdf\User\ReportConexionsService;
use App\Service\Annoucement\ReportPdf\User\ReportForumService;
use App\Service\Annoucement\ReportPdf\User\ReportUserChatService;
use App\Utils\ZipFile;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

class AnnouncementReportZipGenerator
{
    public const TYPE_ANNOUNCEMENT = 'announcement';
    public const TYPE_GROUP = 'announcement-group';
    public const ZIP_TASK_REPORT_NAME = 'announcement-zip';

    private LoggerInterface $logger;
    private EntityManagerInterface $em;
    private string $baseDir;
    private DiplomaService $diplomaService;
    private ReportUserChatService $chatService;
    private PdfAnnouncementGroupService $pdfAnnouncementGroupService;
    private ReportForumService $reportForumService;
    private ReportConexionsService $reportConexionsService;
    private ReportActivitiesService $reportActivitiesService;

    public function __construct(
        KernelInterface             $kernel,
        EntityManagerInterface      $em,
        LoggerInterface             $logger,
        DiplomaService  $diplomaService,
        ReportUserChatService       $chatService,
        PdfAnnouncementGroupService $pdfAnnouncementGroupService,
        ReportForumService          $reportForumService,
        ReportConexionsService      $reportConexionsService,
        ReportActivitiesService     $reportActivitiesService

    ) {
        $this->em = $em;
        $this->baseDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'announcements';
        $this->diplomaService = $diplomaService;
        $this->logger = $logger;
        $this->chatService = $chatService;
        $this->pdfAnnouncementGroupService = $pdfAnnouncementGroupService;
        $this->reportForumService = $reportForumService;
        $this->reportConexionsService = $reportConexionsService;
        $this->reportActivitiesService = $reportActivitiesService;
    }

    /**
     * @param Announcement $announcement
     * @param array $params
     * @param bool $generateZip
     * @return ZipFile|false|string
     */
    public function generateAnnouncement(Announcement $announcement, array $params = [], bool $generateZip = true) {
        $directory = $this->getAnnouncementDirectory($announcement);
        if (!file_exists($directory)) mkdir($directory, 0777, true);

        foreach ($announcement->getAnnouncementGroups() as $group) {
            $this->generateGroup($announcement, $group, $params, false);
        }

        if ($generateZip) {
            $zipFile = new ZipFile();
            $zipFile->setFilename('announcement_' . $announcement->getId())
                ->setPath($directory)
                ->setFiles([$directory])
            ;

            if (!$zipFile->generate() !== true) return FALSE;
            return $zipFile;
        }

        return $directory;
    }

    /**
     * @param Announcement $announcement
     * @param ?AnnouncementGroup $announcementGroup
     * @param array $params
     * @param bool $generateZip
     * @return string|ZipFile|bool
     * @return string if $generateZip is FALSE then return the directory
     * @return ZipFile if $generateZip = true and is success
     * @return bool if fails
     */
    public function generateGroup(Announcement $announcement, ?AnnouncementGroup $announcementGroup = null, array $params = [], bool $generateZip = true) {
        if (!$announcementGroup) {
            if (empty($params['idGroup'])) return FALSE;
            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($params['idGroup']);
        }

        $announcementTutor = $announcementGroup->getAnnouncementTutor();
        if (!$announcementTutor) throw new \Exception('Announcement does not have a defined tutor');

        $groupDirectory = $this->getGroupDirectory($announcement, $announcementGroup);

        if (!file_exists($groupDirectory)) mkdir($groupDirectory, 0777, true);

        /** @var User[] $users */
        $users = $this->em->getRepository(User::class)->createQueryBuilder('u')
            ->select('u')
            ->join('u.announcements', 'au')
            ->where('au.announcementGroup =:group')
            ->setParameter('group', $announcementGroup)
            ->getQuery()
            ->getResult();

        $tutor = $announcementGroup->getAnnouncementTutor()->getTutor();

        foreach ($users as $user) {
            $userDirectory = $this->getPath($groupDirectory, $user->getFullName());
            if (!file_exists($userDirectory)) mkdir($userDirectory);

            $this->generateUserChat($announcement, $tutor, $user, $userDirectory, $params);
            $this->generateUserConnections($announcement, $user, $userDirectory, $params);
            $this->generateUserMessages($announcement, $user, $userDirectory, $params);
            $this->generateUserActivities($announcement, $user, $userDirectory, $params);
            $this->generateUserActivityDetails($announcement, $user, $userDirectory, $params);
            $this->generateUserCertificates($announcement, $user, $userDirectory, $params);
        }

        $this->generateGroupData($announcementGroup, $announcement, $groupDirectory, $params);
        $this->generateForumData($announcement, $users, $groupDirectory, $params);
        $this->generateVideoConference($announcement, $announcementGroup, $groupDirectory, $params);
        $this->generateTeaching($announcement, $announcementGroup, $groupDirectory, $params);
        $this->generateTeachingInterventions($announcement, $announcementGroup, $groupDirectory, $params);
        $this->generateUserAssistance($announcement, $announcementGroup, $groupDirectory, $params);

        if ($generateZip) {
            $zipFile = new ZipFile();
            $zipFile->setFilename('group_' . $announcementGroup->getId())
                ->setPath($groupDirectory)
                ->setFiles([$groupDirectory])
            ;

            if (!$zipFile->generate() !== true) return FALSE;
            return $zipFile;
        }

        return $groupDirectory;
    }

    /**
     * ******************************
     * General documentation        *
     * ******************************
     */

    /**
     * @param AnnouncementGroup $group
     * @param string $groupDirectory
     * @param array $params
     * @return void
     */
    public function generateGroupData(AnnouncementGroup $group = null, Announcement $announcement, string $dir, array $params)
    {
        $pdf = $params['PDF']['groupData'] ?? false;
        $excel = $params['EXCEL']['groupData'] ?? false;



        if ($pdf) {
            $path = $this->getPath($dir, null, 'groups.pdf');
            $this->pdfAnnouncementGroupService->generatePdfGroup(
                $group,
                $announcement,
                $path
            );
        }
    }

    /**
     * @param Announcement $announcement
     * @param array $users
     * @param string $dir
     * @param User[] $params
     * @return void
     */
    public function generateForumData(Announcement $announcement, array $users, string $dir, array $params)
    {
        $pdf = $params['PDF']['forum'] ?? false;
        $excel = $params['EXCEL']['forum'] ?? false;
        $forumChannel = $this->em->getRepository(Announcement::class)->getForumChannel($announcement);
        if ($pdf) {
            $path = $this->getPath($dir, null, 'forum.pdf');
            $this->reportForumService->generatePdf($forumChannel, $path);
        }
    }

    public function generateVideoConference(Announcement $announcement, AnnouncementGroup $group, string $dir, array $params)
    {
        $pdf = $params['PDF']['videoconference'] ?? false;
        $excel = $params['EXCEL']['videoconference'] ?? false;
    }

    public function generateTeaching(Announcement $announcement, AnnouncementGroup $group, string $dir, array $params)
    {
        $pdf = $params['PDF']['teaching'] ?? false;
        $excel = $params['EXCEL']['teaching'] ?? false;
    }

    public function generateTeachingInterventions(Announcement $announcement, AnnouncementGroup $group, string $dir, array $params)
    {
        $pdf = $params['PDF']['teacherInterventions'] ?? false;
        $excel = $params['EXCEL']['teacherInterventions'] ?? false;
    }

    public function generateUserAssistance(Announcement $announcement, AnnouncementGroup $group, string $dir, array $params)
    {
        $pdf = $params['PDF']['assistance'] ?? false;
        $excel = $params['EXCEL']['assistance'] ?? false;
    }

    /**
     * **************************
     * Per user documentation   *
     * **************************
     */

    public function generateUserMessages(Announcement $announcement, User $user, string $dir, array $params)
    {
        $pdf = $params['PDF']['messages'] ?? false;
        $excel = $params['EXCEL']['messages'] ?? false;
    }

    public function generateUserChat(Announcement $announcement, User $tutor, User $user, string $dir, array $params)
    {
        $pdf = $params['PDF']['chat'] ?? false;
        $excel = $params['EXCEL']['chat'] ?? false;

        $directChannel = $this->em->getRepository(Announcement::class)->getDirectChatChannel($announcement);
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $user
        ]);

        if ($pdf) {
            $path = $this->getPath($dir, null, 'chat.pdf');
            $this->chatService->generatePdf(
                $directChannel,
                $tutor,
                $user,
                $announcementUser,
                $path

            );
        }
    }


    public function generateUserConnections(Announcement $announcement, User $user, string $dir, array $params)
    {
        $pdf = $params['PDF']['connection'] ?? false;
        $excel = $params['EXCEL']['connection'] ?? false;

        $announcementUser = $this->getAnnouncementUser($announcement, $user);

        if ($pdf) {
            $path = $this->getPath($dir, null, 'connection.pdf');
            $this->reportConexionsService->generatePdfConnection(
                $announcementUser,
                $path
            );
        }
    }

    public function generateUserActivities(Announcement $announcement, User $user, string $dir, array $params)
    {
        $pdf = $params['PDF']['activities'] ?? false;
        $excel = $params['EXCEL']['activities'] ?? false;

        $announcementUser = $this->getAnnouncementUser($announcement, $user);

        if ($pdf) {
            $path = $this->getPath($dir, null, 'activities.pdf');
            $this->reportActivitiesService->generatePdfActivities(
                $announcementUser,
                $path
            );
        }
    }

    public function generateUserActivityDetails(Announcement $announcement, User $user, string $dir, array $params)
    {
        $pdf = $params['PDF']['activitiesDetails'] ?? false;
        $excel = $params['EXCEL']['activitiesDetails'] ?? false;
    }

    public function generateUserCertificates(Announcement $announcement, User $user, string $dir, array $params)
    {
        $pdf = $params['PDF']['activities'] ?? false;
        $excel = $params['EXCEL']['activities'] ?? false;

        if ($pdf) {
            $path = $this->getPath($dir, null, 'Certificate.pdf');
            $b64 = $this->diplomaService->generateUserRequestedDiploma($announcement, $user, false, $path);
            //            $this->logger->warning('b64', [$b64]);
            //            file_put_contents($path, 'data:application/pdf;base64,' . $b64);
        }
    }

    private function getAnnouncementDirectory(Announcement $announcement): string {
        return $this->baseDir . DIRECTORY_SEPARATOR . "announcement_" . $announcement->getId();
    }

    private function getGroupDirectory(Announcement $announcement, AnnouncementGroup $group): string {
        return $this->getAnnouncementDirectory($announcement) . DIRECTORY_SEPARATOR . 'group_' . $group->getId();
    }

    private function getPath(string $path, string $intermediary = null, string $filename = null): string
    {
        $fullPath = substr($path, -1) === DIRECTORY_SEPARATOR ? $path : $path . DIRECTORY_SEPARATOR;
        if (!empty($intermediary)) $fullPath .= $intermediary;
        if (!empty($filename)) $fullPath .= DIRECTORY_SEPARATOR . $filename;
        return $fullPath;
    }

    private function getAnnouncementUser(Announcement $announcement, User $user): ?AnnouncementUser
    {
        return $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $user
        ]);
    }
}
