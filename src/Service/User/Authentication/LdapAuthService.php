<?php

namespace App\Service\User\Authentication;

use App\Entity\User;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Ldap\Adapter\ExtLdap\Adapter;
use Symfony\Component\Ldap\Entry;
use Symfony\Component\Ldap\Exception\ConnectionException;
use Symfony\Component\Ldap\Ldap;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class LdapAuthService
{
    private LoggerInterface $logger;
    private EntityManagerInterface $em;
    private SettingsService $settings;
    private UserPasswordHasherInterface $passwordHasher;
    protected TranslatorInterface    $translator;

    private Adapter $ldapAdapter;
    private Ldap $ldap;
    private string $ldapServiceDn;

    public function __construct(
//        Adapter $ldapAdapter,
        EntityManagerInterface $em,
        UserPasswordHasherInterface $passwordHasher,
        LoggerInterface $logger,
        SettingsService $settings,
        TranslatorInterface    $translator
    )
    {
        try
        {
            $this->passwordHasher = $passwordHasher;
            $this->em = $em;
            $this->settings = $settings;
            $this->logger = $logger;
            $this->translator = $translator;


//            if ($this->settings->get('ldap.enabled')) {
//                $this->ldapServiceDn = $this->settings->get('ldap.service_dn');
//                $ldapServiceUser = $this->settings->get('ldap.service_user');
//                $ldapServicePassword = $this->settings->get('ldap.service_password');
//
//                $this->ldapAdapter = new Adapter(
//                    [
//                        'connection_string' => $this->settings->get('ldap.connection_string')
//                    ]
//                );
//                $this->ldap =  new Ldap($this->ldapAdapter);
//                $this->ldap->bind(implode(',', [$ldapServiceUser, $this->ldapServiceDn]), $ldapServicePassword);
//            }
        }
        catch(\Exception $e)
        {
            $this->logger->error($e->getMessage());
        }
    }

    // user customFilters assigned => no pueden borrarse

    /**
     * @param string $username
     * @param string $password
     * @return false | Entry
     */
    public function getEntryFromActiveDirectory(string $username, string $password) {
        $userIdentifier = $this->settings->get('ldap.user_identifier');

        $ldap = new Ldap($this->ldapAdapter);
        $search = false;

        try {
            $ldap->bind(implode(',', [$userIdentifier . "=" . $username, $this->ldapServiceDn]), $password);
            if ($this->ldapAdapter->getConnection()->isBound()) {
                $search = $ldap->query(
                    $this->settings->get('ldap.search_dn'),
                    $userIdentifier . '=' . $username
                )->execute()->toArray();
            }
        } catch (ConnectionException $exception) {}

        if ($search && count($search) === 1) {
            return $search[0];
        }

        return false;
    }

    public function insertNewUser(Entry $entry, string $password): User
    {
        $user = new User();

        $user->setEmail((string)($entry->getAttribute('mail') ?? [])[0])
            ->setPassword($this->passwordHasher->hashPassword($user, $password))
            ->setFirstName((string)($entry->getAttribute('firstName') ?? ['Undefined'])[0])
            ->setLastName((string)($entry->getAttribute('lastName') ?? ['Undefined'])[0])
            ->setIsActive(true)
            ->setOpen(true)
            ->setCode((string)($entry->getAttribute('employeeID') ?? [null])[0])
            ->setRoles([User::ROLE_USER])
        ;

        /**
         * Create filters based on ldap entry data
         */

        $this->em->persist($user);
        $this->em->flush();
        return $user;
    }

    //IPADDRESS
    // CRON
    //BaseDN => dc=example,dc=com
    //SearchDN => cn=read-only-admin,dc=example,dc=com
    // Password => password
    //
    // User
    // SearchDN
    // Password


    public function loginLdapUser(string $username, string $password, array $options = [])
    {
        $this->logger->error('loginLdapUser');
        $userIdentifier = $options['userIdentifier'] ?? 'uid';
        $connectionString = $options['connectionString'] ?? null;
        $serviceDn = $options['serviceDn'] ?? null;
        $searchDn = $options['searchDn'] ?? null;

        if (empty($connectionString) || empty($serviceDn)) throw new \RuntimeException("Connection string and serviceDn required");
        if (empty($userIdentifier)) throw new \RuntimeException("No LDAP userIdentifier has been defined");

        $adapter = new Adapter(
            [
                'connection_string' => $connectionString
            ]
        );

        $ldap = new Ldap($adapter);
//        $petition = implode(',', ["cn=read-only-admin", $serviceDn]);
        $petition = implode(',', ["cn=read-only-admin", $serviceDn]);
        $this->logger->error('User: ' . $petition);

        try {
            $ldap->bind("cn=read-only-admin,dc=example,dc=com", "password");
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), $e->getTrace());
            throw new \RuntimeException($e);
        }

        $this->logger->error("Connected");

        if (!$adapter->getConnection()->isBound()) {
            throw new \RuntimeException(
                $this->translator->trans(
                    'message_api.base.credentials_no_correct',
                    [],
                    'message_api',
                    $this->settings->get('app.defaultLanguage') // Try to get user if exists for correct translation
                )
            );
        }


        $searchQuery = "(objectclass=*)";
        $baseDN = "dc=example,dc=com";
        $this->logger->error('Search', [
            'baseDn' => $baseDN,
            'query' => $searchQuery,
        ]);

        try {
            $userSearch = $ldap->query(
                $baseDN,
                $searchQuery
            )->execute()->toArray();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), $e->getTrace());
            return [];
        }

        $this->logger->error('UserSearch ' . count($userSearch));
        foreach ($userSearch as $u) {
            $this->logger->error('User', $u->getAttributes());
        }

//        if ($userSearch && count($userSearch) === 1) {
//            /** @var Entry $ldapEntry */
//            $ldapEntry = $userSearch[0];
//
//            return $ldapEntry->getAttributes();
//        }

        return [];
    }

    /**
     * Insert or update user
     * @param Entry $entry
     * @param string $password
     * @param User|null $u
     * @return ?User
     */
    public function handleLdapEntry(Entry $entry, string $password, ?User $u = null): ?User
    {
        $mail = ($entry->getAttribute('mail') ?? [])[0];
        if (empty($mail)) {
            $this->logger->error('Mail has not been returned');
            return null;
        }
        $user = $u;
        if (!$user) {
            // Try to find user with mail param;
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $mail]);
            if (!$user) {
                $user = new User();
                $user->setIsActive(true)
                    ->setOpen(true);
            }
        }
        $user->setEmail((string)($entry->getAttribute('mail') ?? [])[0])
            ->setPassword($this->passwordHasher->hashPassword($user, $password))
            ->setFirstName((string)($entry->getAttribute('firstName') ?? ['Undefined'])[0])
            ->setLastName((string)($entry->getAttribute('lastName') ?? ['Undefined'])[0])
            ->setCode((string)($entry->getAttribute('employeeID') ?? [null])[0])
            ->setRoles([User::ROLE_USER])
        ;

        /**
         * Create filters based on ldap entry data
         */

        $this->em->persist($user);
        $this->em->flush();
        return $user;
    }

    /**
     * @param $uid
     * @param $password
     * @return ?User
     */
    public function login($uid, $password): ?User
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $uid]);
        $ldapEntry = $this->getEntryFromActiveDirectory($uid, $password);
        if (!$ldapEntry || get_class($ldapEntry) !== Entry::class) {
            return null;
        }
        return $this->handleLdapEntry($ldapEntry, $password, $user);
    }
}
