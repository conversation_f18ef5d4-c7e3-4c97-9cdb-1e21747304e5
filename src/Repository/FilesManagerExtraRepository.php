<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\FilesManagerExtra;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FilesManagerExtra>
 *
 * @method FilesManagerExtra|null find($id, $lockMode = null, $lockVersion = null)
 * @method FilesManagerExtra|null findOneBy(array $criteria, array $orderBy = null)
 * @method FilesManagerExtra[]    findAll()
 * @method FilesManagerExtra[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FilesManagerExtraRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FilesManagerExtra::class);
    }

    public function add(FilesManagerExtra $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FilesManagerExtra $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
