<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Center;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Center|null find($id, $lockMode = null, $lockVersion = null)
 * @method Center|null findOneBy(array $criteria, array $orderBy = null)
 * @method Center[]    findAll()
 * @method Center[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CenterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Center::class);
    }

    public function getList()
    {
        $centers = $this->findBy([], ['name' => 'ASC']);
        $centersList = [];
        foreach ($centers as $center) {
            $centersList[$center->getName()] = $center->getId();
        }

        return $centersList;
    }

    public function findList($centers = [])
    {
        $query = $this->createQueryBuilder('c');

        if (!empty($centers)) {
            $query->andWhere($query->expr()->in('c.id', $centers));
        }

        return $query
            ->orderBy('c.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }
}
