<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\SettingGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SettingGroup>
 *
 * @method SettingGroup|null find($id, $lockMode = null, $lockVersion = null)
 * @method SettingGroup|null findOneBy(array $criteria, array $orderBy = null)
 * @method SettingGroup[]    findAll()
 * @method SettingGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SettingGroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SettingGroup::class);
    }

    public function add(SettingGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SettingGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
