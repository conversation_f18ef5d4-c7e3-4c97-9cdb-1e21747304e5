<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\PagesTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PagesTranslation>
 *
 * @method PagesTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method PagesTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method PagesTranslation[]    findAll()
 * @method PagesTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PagesTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PagesTranslation::class);
    }

    public function add(PagesTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(PagesTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
