{% block head_stylesheets %}
  {{ encore_entry_link_tags('visorPdf') }}
{% endblock %}

<style>
  body {
    margin: 0px;
    background: #c7c7c7;
  }
  
  .not-content {
    display: flex;
    justify-content: center;
    aling-items: center;
    margin: auto;
    color: white;
    align-items: center;
    flex-direction: column;
    height: 100%;
  }
</style>

{% if pdf %}
  <div id="visor-pdf">
    <visor-pdf :id="{{ chapter.id }}" :namepdf="'{{ pdf.pdf }}'" :is_downloadable="{{ pdf.isDownloadable ? 1 : 0 }}"></visor-pdf>
  </div>
{% else %}
  <div class="not-content">
    <h1>{{ 'message_api.player.no_content'|trans({}, 'message_api', user.locale) }}</h1>
  </div>
{% endif %}

<script>
    {% if token is not null %}
    localStorage.setItem('token', '{{ token }}');   
    {%  else %}
    localStorage.setItem('token', localStorage.getItem('user-token'));
    {% endif %}
</script>

{% block body_javascript %}
  {{ encore_entry_script_tags('visorPdf') }}
{% endblock %}
