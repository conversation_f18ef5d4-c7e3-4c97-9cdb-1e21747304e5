{% extends 'layout/pdf.html.twig' %}

   {% block stylesheets %}
       {{ encore_entry_link_tags('pdf') }}
   {% endblock %}

{% block body %}
    <div class="subsidizer-report">
        <h1>{{ 'announcements.configureFields.title_report'|trans({}, 'messages',  app.user.locale) }}
            <span>({{ 'now' | date('d/m/Y H:i') }})</span></h1>
        {% include 'admin/subsidizer/report/info_course.html.twig' %}
        {% include 'admin/subsidizer/report/content_course.html.twig' %}

        {% if announcementUsers %}
            {% include 'admin/subsidizer/report/user_announcement.html.twig' %}
        {% endif %}

        {% include 'admin/subsidizer/report/details_user.html.twig' %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ encore_entry_script_tags('pdf') }}
{% endblock %}


