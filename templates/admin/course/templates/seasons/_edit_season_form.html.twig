<div class="modal fade" id="editSeason{{season.id}}" tabindex="-1" aria-labelledby="editSeasonLabel{{season.id}}" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title px-2" id="editSeasonLabel{{season.id}}">{{ 'common_areas.edit'|trans({}, 'messages') }}:
                    {{season.name}}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/admin/edit-season/{{season.id}}/{{course.id}}">
                    <div class="col-12">
                        <label for="name">{{ 'Name'|trans({}, 'messages') }}:
                        </label>
                        <input class="form-control" type="text" id="name" name="name" value='{{season.name}}' required><br>
                    </div>
                    <div class="col-12 mb-1">
                        <label for="type" class="form-label">Tipo</label>
                        <select class="form-select" name="type" id="type">
                            {% for key, type in seasonTypes %}
                                {% if key is same as season.type %}
                                    <option value="{{ key }}" selected>{{ type }}</option>
                                {% else %}
                                    <option value="{{ key }}">{{ type }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="my-3 pr-3 text-right">
                        <input type="submit" class="btn btn-primary" value="{{ 'common_areas.edit'|trans({}, 'messages') }}">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
