{% extends 'layout/pdf.html.twig' %}

{% block stylesheets %}
  {{ encore_entry_link_tags('pdf') }}
{% endblock %}

{% block body %}
  <div class="subsidizer-report">
    <h1>
      {{ 'announcements.configureFields.title_report'|trans({}, 'messages', app.user.locale) }}
      <span>({{ 'now'|date('d/m/Y H:i') }})</span>
    </h1>

    {% include 'fundae/report_group/info_course.html.twig' %}
    {% include 'fundae/report_group/content_course.html.twig' %}

    {% if group %}
      {{ include('fundae/report_group/info_group.html.twig', { informationGroup: group.groupInfo, users: group.users }) }}
    {% endif %}
  </div>
{% endblock %}

{% block javascripts %}
  {{ encore_entry_script_tags('pdf') }}
{% endblock %}
