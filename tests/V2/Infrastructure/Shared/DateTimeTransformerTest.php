<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared;

use App\V2\Infrastructure\Shared\DateTimeTransformer;
use DateTimeImmutable;
use DateTimeInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class DateTimeTransformerTest extends TestCase
{
    /**
     * Test that toOutput correctly formats a DateTimeInterface to a string.
     */
    #[DataProvider('dateTimeProvider')]
    public function testToOutput(\DateTimeInterface $dateTime, string $expected): void
    {
        // Act
        $result = DateTimeTransformer::toOutput($dateTime);

        // Assert
        $this->assertSame($expected, $result);
    }

    /**
     * Test that fromInput correctly parses a string to a DateTimeImmutable.
     */
    #[DataProvider('stringDateTimeProvider')]
    public function testFromInput(string $dateTimeString, string $expected): void
    {
        // Act
        $result = DateTimeTransformer::fromInput($dateTimeString);

        // Assert
        $this->assertInstanceOf(\DateTimeImmutable::class, $result);
        $this->assertSame($expected, $result->format(DateTimeTransformer::DATE_TIME_FORMAT));
    }

    /**
     * Test that fromDateTime correctly converts a DateTimeInterface to a DateTimeImmutable.
     */
    #[DataProvider('dateTimeProvider')]
    public function testFromDateTime(\DateTimeInterface $dateTime, string $expected): void
    {
        // Act
        $result = DateTimeTransformer::fromDateTime($dateTime);

        // Assert
        $this->assertInstanceOf(\DateTimeImmutable::class, $result);
        $this->assertSame($expected, $result->format(DateTimeTransformer::DATE_TIME_FORMAT));
    }

    /**
     * Data provider for testToOutput and testFromDateTime.
     */
    public static function dateTimeProvider(): \Generator
    {
        yield 'date_time_with_zero_seconds' => [
            'dateTime' => new \DateTimeImmutable('2023-01-15 10:30:00'),
            'expected' => '2023-01-15 10:30:00',
        ];

        yield 'date_time_with_non_zero_seconds' => [
            'dateTime' => new \DateTimeImmutable('2023-01-15 10:30:45'),
            'expected' => '2023-01-15 10:30:45',
        ];

        yield 'date_time_with_microseconds' => [
            'dateTime' => new \DateTimeImmutable('2023-01-15 10:30:45.123456'),
            'expected' => '2023-01-15 10:30:45',
        ];
    }

    /**
     * Data provider for testFromInput.
     */
    public static function stringDateTimeProvider(): \Generator
    {
        yield 'date_time_with_zero_seconds' => [
            'dateTimeString' => '2023-01-15 10:30:00',
            'expected' => '2023-01-15 10:30:00',
        ];

        yield 'date_time_with_non_zero_seconds' => [
            'dateTimeString' => '2023-01-15 10:30:45',
            'expected' => '2023-01-15 10:30:45',
        ];
    }
}
