<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI\Transformer;

use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\LTI\Transformer\LtiToolTransformer;
use OAT\Library\Lti1p3Core\Tool\Tool as CoreTool;
use PHPUnit\Framework\TestCase;

class LtiToolTransformerTest extends TestCase
{
    /**
     * @throws InvalidUuidException
     */
    public function testFromLtiToolToCoreTool()
    {
        $toolId = UuidMother::create();

        $ltiTool = LtiToolMother::create(
            id: $toolId,
            name: 'Tool example',
            audience: 'Tool example audience',
            oidcInitiationUrl: new Url('http://exampletool.com/init'),
            launchUrl: new Url('http://exampletool.com/launch'),
            deepLinkingUrl: new Url('http://exampletool.com/deeplinking'),
        );
        $this->assertEquals(
            new CoreTool(
                identifier: $toolId->value(),
                name: 'Tool example',
                audience: 'Tool example audience',
                oidcInitiationUrl: 'http://exampletool.com/init',
                launchUrl: 'http://exampletool.com/launch',
                deepLinkingUrl: 'http://exampletool.com/deeplinking',
            ),
            LtiToolTransformer::fromLtiToolToCoreTool($ltiTool),
        );
    }
}
