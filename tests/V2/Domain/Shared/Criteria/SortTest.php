<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Criteria;

use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortDirection;
use PHPUnit\Framework\TestCase;

class SortTest extends TestCase
{
    /**
     * Test that the constructor sets the field and direction correctly.
     */
    public function testConstructor(): void
    {
        // Arrange
        $field = new SortableField('name');
        $direction = SortDirection::ASC;

        // Act
        $sort = new Sort($field, $direction);

        // Assert
        $this->assertSame($field, $sort->getField());
        $this->assertSame($direction, $sort->getDirection());
    }

    /**
     * Test that getField returns the correct field.
     */
    public function testGetField(): void
    {
        // Arrange
        $field = new SortableField('email');
        $sort = new Sort($field, SortDirection::DESC);

        // Act
        $result = $sort->getField();

        // Assert
        $this->assertSame($field, $result);
    }

    /**
     * Test that getDirection returns the correct direction.
     */
    public function testGetDirection(): void
    {
        // Arrange
        $direction = SortDirection::DESC;
        $sort = new Sort(new SortableField('id'), $direction);

        // Act
        $result = $sort->getDirection();

        // Assert
        $this->assertSame($direction, $result);
    }
}
