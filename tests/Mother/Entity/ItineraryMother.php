<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Itinerary;
use App\Entity\User;

class ItineraryMother
{
    public const string DEFAULT_NAME = 'Example Itinerary Name';
    public const string DEFAULT_DESCRIPTION = 'Itinerary Description';
    public const bool DEFAULT_STATE = true;

    public static function create(
        ?int $id = null,
        ?string $name = null,
        ?string $description = null,
        ?bool $active = null,
        ?int $sort = null,
        ?User $createdBy = null,
    ): Itinerary {
        $itinerary = new Itinerary();

        if (null !== $id) {
            $itinerary->setId($id);
        }
        $itinerary->setName($name ?? self::DEFAULT_NAME);
        $itinerary->setDescription($description ?? self::DEFAULT_DESCRIPTION);
        $itinerary->setActive($active ?? self::DEFAULT_STATE);
        $itinerary->setSort($sort);
        $itinerary->setCreatedBy($createdBy);

        return $itinerary;
    }
}
